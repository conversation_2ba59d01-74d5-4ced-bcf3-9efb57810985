{"BusatySchool": "Busaty - School", "Busaty - School": "Busaty - School", "checkPassword": "Check your Password", "checkEmail": "Email Created Successfully now check your email for verification code", "home": "Home", "username": "Username", "code": "Code", "line": "line", "birthdate": "birthdate", "classrooms": "classrooms", "classroom": "classroom", "add_classroom": "add_classroom", "classroom_name": "classroom_name", "options": "options", "children": "children", "login_with_google": "login with Google", "check_connection": "Please check your internet connection", "account_disabled": "This account has been disabled", "login_failed": "<PERSON><PERSON> failed. Please try again", "new_address": "new_address", "add_student_to_bus": "Add Students to Bus", "bus_name": "Bus name", "login": "<PERSON><PERSON>", "notes": "Notes", "email": "Email", "password": "Password", "notHaveAccount": "Not Have Account ? ", "createAccount": "Create Account", "forgetPassword": "Forget Password ? ", "remember": "Remember", "signup": "Signup", "name": "Name", "schoolName": "School Name", "phoneNumber": "Phone Number", "confirmPassword": "Confirm Password", "haveAccount": "Have Account ? ", "sendCodeAgain": "send code again", "sendCode": "Send Code", "forget": "Forget Password", "sendCodeRegister": "code will be sent to the registered email to recover the account", "newPassword": "New Password", "againPassword": "Write Again Password", "passwordChangeSuccess": "Password Change Success", "goHome": "Go to Home", "next": "Next", "searchStudentHint": "search the student", "searchDriverHint": "search the driver", "addByHand": "Add student ", "addByFile": "Add students file", "studentName": "student name", "bloodType": "blood type", "dateOfBirth": "Date of birth", "educationalLevel": "educational level", "city": "city", "addBus": "add bus", "add": "Add", "addFile": "Add File", "address": "Address", "nationalId": "National ID", "allDrivers": "All Drivers", "allSupervisors": "All Supervisors", "searchSupervisorHint": "Search Supervisor", "addStudent": "Add Student", "editStudent": "Edit Student Data", "addStudentFile": "Add Student File", "studentData": "Student Data", "students": "Students", "DriveData": "Driver Data", "SupervisorData": "Supervisor Data", "showStudentData": "Student Data", "addDriver": "Add Driver", "editDriver": "Edit Driver Data", "addSupervisor": "Add Supervisor", "editSupervisor": "Edit Supervisor Data", "itinerary": "ITinerary", "busNumber": "Bus Number", "supervisor": "Supervisor", "driver": "Driver", "allBus": "All Bus", "searchBusHint": "Search Bus", "addStudentBusFile": "Add Student to Bus From File", "show": "Show", "edit": "Edit", "delete": "Delete", "addStudentManually": "Add Student Manually", "addStudentFromFile": "Add Student From File", "showStudent": "Show Student", "addStudentToBusFromFile": "Add Student To Bus From File", "addStudentToBusManually": "Add Student To Bus Manually", "search": "Search", "setting": "Setting", "languages": "Languages", "help": "Help", "profile": "Profile", "english": "English", "arabic": "العربية", "updateProfile": "Update Profile", "changePassword": "Change Password", "parent": "Parent", "searchParentHint": "Search Parent", "showParent": "Show Parent", "parentData": "Parent Data", "oldPassword": "Old Password", "save": "Save", "allRequests": "All Requests", "newRequests": "New Requests", "requestsPending": "Pending Request", "acceptRequest": "Accept Requests", "refusalRequest": "Refusal Requests", "requestChangeAddress": "Request Change Address", "accept": "Accept", "refusal": "Refusal", "reasonRefusal": "Reason Refusal", "refuseRequest": "Refuse Requests", "showRequest": "Show Request", "validPhone": "please write phone number", "validName": "please write name", "validAddress": "please write address", "validEmail": "please write email", "validPassword": "please write password", "validConfirmPassword": "please write confirmed password", "supervisors": "Supervisors", "drivers": "Drivers", "parents": "Parents", "bus": "Buses", "requestsChangeAddress": "Change address Requests", "exit": "Exit", "validNewPassword": "please enter new password", "validConfirmedNewPassword": "please enter confirmed new password", "validOldPassword": "please enter old password", "addStudentByLine": "Add Student By Line", "type": "Type", "grade": "Grade", "birthDate": "Birth Date", "phone": "Phone", "studentNotFound": "Students Not Found", "busesNotFound": "Buses Not Found", "requestAbsencesNotFound": "There are no requests", "driverNotFound": "Drivers Not Found", "parentsNotFound": "Parents Not Found", "carNumber": "Car Number", "gender": "Gender", "religion": "Religion", "typeBlood": "Type Blood", "getLocations": "Get Locations", "locationDone": "Add Location Successful", "setLocation": "Set Your Location", "email_verified": "Your email is verified.", "wrong_code": "wrong code! Please check your email and try again", "location": "Location", "supervisor_current_location": "Supervisor Current Location", "driver_current_location": "Driver Current Location", "is_required": " is required", "absence_requests": "Absence Requests", "selectGrade": "Select Grade", "selectGender": "Select Gender", "selectReligion": "Select Religion", "selectBloodType": "Select Blood Type", "selectBus": "Select Bus", "successfully_done": "Successfully Done", "copy": "Copy", "student": "Student", "copied": "Code Copied", "not_required": "Not Required", "required": "Required", "morning_trip": "Morning Trip", "evening_trip": "Evening Trip", "full_day": "Full Day", "extract_excel": "Extract Excel File", "download_pdf": "Download PDF File", "student_address": "Student Address", "select_edu_lev": "Select Grade first", "download_example_file": "Download Example Excel file", "students_file": "Students File (Excel)", "bus_subscription": "Bus Subscription", "not_found": "Not Found", "driver_added": "Driver added successfully", "driver_edited": "Driver edited successfully", "supervisor_added": "Supervisor added successfully", "supervisor_edited": "Supervisor edited successfully", "student_added": "Student added successfully", "students_added": "Students added successfully", "student_edited": "Student edited successfully", "busStudents": "Bus Students", "call": "Call", "showSons": "Show Sons", "sons_number": "Sons Number", "sons": "Sons", "notifications": "Notifications", "good_morning": "Good Morning", "good_evening": "Good Evening", "and": "and", "not_match": "doesn't match", "yes": "Yes", "no": "No", "selectGradeFirst": "Select Grade First", "selectBusFirst": "Select Bus First", "request_status": "Request Status", "accepted": "Accepted", "refused": "Refused", "newS": "New", "duplicated_user": "Username is already reserved, please change it", "editBus": "Edit Bus", "busName": "Bus Name", "notFound": "Not Found", "goodMorning": "Good Morning", "goodEvening": "Good Evening", "morningTrip": "Morning Trip", "isRequired": "Is Required", "addClassroom": "Add ClassRoom", "duplicatedUser": "Duplicated User", "addStudentToBus": "Add Student To Bus", "driverAdded": "Driver Added", "driverEdited": "Driver Edited", "eveningTrip": "Evening Trip", "fullDay": "Full Day", "busSubscription": "Bus Subscription", "studentAddress": "Student Address", "notRequired": "Not Required", "emailVerified": "<PERSON><PERSON>", "driverCurrentLocation": "Driver Current Location", "driveData": "Driver Data", "newAddress": "New Address", "supervisorCurrentLocation": "supervisor Current Location", "wrongCode": "Wrong Code", "absenceRequests": "absence Requests", "requestStatus": "Request Status", "newRequest": "New Request", "successfullyDone": "Successfully Done", "sonsNumber": "Sons Number", "notMatch": "Not Match", "extractExcel": "Extract Excel", "downloadPDF": "Download PDF", "supervisorEdited": "Supervisor Edited", "supervisorAdded": "Supervisor Added", "studentsAdded": "Students Added", "selectEduLev": "Select Grade", "classroomName": "Class Room Name", "studentsFile": "Students File", "studentEdited": "Student Edited", "studentAdded": "Student Added", "downloadExampleFile": "Download Example File", "checkInternetConnection": "Check your Internet Connection", "openTrips": "Open Trips", "tripTypes": "Trip Type", "supervisorName": "Supervisor Name", "old_address": "Old Address", "contact_us": "Contact Us", "get_in_touch": "Get in Touch", "wed_love_to_hear": "We'd love to hear from you", "enter_your_name": "Enter your name", "enter_your_email": "Enter your email", "describe_problem": "Describe your problem or feedback", "please_enter_name": "Please enter your name", "please_enter_email": "Please enter your email", "please_valid_email": "Please enter a valid email", "please_describe_problem": "Please describe your problem", "message_too_long": "Message cannot be longer than 1000 characters", "contact_directly": "Or contact us directly:", "email_copied": "Email copied to clipboard", "sending": "Sending...", "email_sent": "<PERSON><PERSON> sent successfully!", "failed_to_send": "Failed to send email: {error}", "share": "Share", "subscriptionRequired": "Subscription Required", "subscriptionMessage": "Please subscribe to access this feature", "subscribe": "Subscribe", "cancel": "Cancel", "apply_coupon": "Apply Coupon", "apply": "Apply", "benefits": "Benefits", "subscription": "Subscription", "subscribe_with_coupon": "Subscribe with Coupon", "already_subscribed": "Already Subscribed", "realTimeNotifications": "Real-time Notifications", "realTimeNotificationsDesc": "Get instant alerts about your child's bus journey", "liveTracking": "Live Tracking", "liveTrackingDesc": "Track your child's bus location in real-time", "tripHistory": "Trip History", "tripHistoryDesc": "View detailed history of all bus trips", "prioritySupport": "Priority Support", "prioritySupportDesc": "Get premium customer support 24/7", "busManagement": "Bus Management", "busManagementDesc": "Efficiently manage and monitor your school's bus fleet with comprehensive tracking and scheduling features", "staffManagement": "Staff Management", "staffManagementDesc": "Manage supervisors and drivers, assign them to buses, and monitor their performance in real-time", "realTimeTracking": "Real-time Tracking", "realTimeTrackingDesc": "Track all school buses in real-time, monitor routes, and ensure safe transportation of students", "attendanceSystem": "Attendance System", "attendanceSystemDesc": "Monitor student attendance, track pick-up and drop-off times, and generate detailed reports", "enter_coupon_description": "Enter your free Busaty coupon code to enjoy one year of complimentary access to all app features from the date of registration", "update_required": "Update Required", "update_message": "A new version of the app is available. Please update to continue using the app.", "update": "Update", "notification_settings": "Notification Settings", "trip_notifications": "Trip Notifications", "enable_trip_notifications": "Enable Trip Notifications", "address_change_notifications": "Address Change Notifications", "enable_address_change_notifications": "Enable Address Change Notifications", "absence_notifications": "Absence Notifications", "enable_absence_notifications": "Enable Absence Notifications", "initialize_notifications": "Initialize Notifications", "pro": "PRO", "deleteAccount": "Delete Account", "student_location": "Student Location", "location_not_available": "Location not available", "latitude": "Latitude", "longitude": "Longitude", "view_on_map": "View on Map", "open_in_maps": "Open in Maps", "show_full_map": "Show Full Map", "startDate": "Start Date", "endDate": "End Date", "remainingTime": "Remaining Time", "expired": "Expired", "months": "Months", "days": "Days", "unknown": "Unknown", "complete_profile": "Complete Profile", "personal_information": "Personal Information", "upload_photo": "Upload Photo", "please_enter_valid_name": "Please enter a valid name", "please_enter_valid_phone": "Please enter a valid phone number", "please_enter_valid_address": "Please enter a valid address", "please_upload_photo": "Please upload a photo", "profile_completed": "Profile completed successfully", "something_went_wrong": "Something went wrong", "submit": "Submit", "previousTrips": "Previous Trips", "noTripsYet": "No previous trips found", "filterByBus": "Filter by Bus", "filterByDate": "Filter by Date", "tripStartTime": "Start Time", "tripEndTime": "End Time", "viewRoute": "View Route", "viewAttendance": "View Attendance", "applyFilters": "Apply Filters", "resetFilters": "Reset Filters", "tripType": "Trip Type", "all": "All", "attendance": "Attendance", "present": "Present", "absent": "Absent", "attendanceDetails": "Attendance Details", "studentsList": "Students List", "noStudentsPresent": "No students present", "noStudentsAbsent": "No students absent", "attendanceDate": "Attendance Date", "attendanceStatus": "Attendance Status", "routeMap": "Route Map", "tripRoute": "Trip Route", "startPoint": "Start Point", "endPoint": "End Point", "busRoute": "Bus Route", "routeDetails": "Route Details", "totalDistance": "Total Distance", "estimatedTime": "Estimated Time", "noRouteData": "No route data available", "kilometers": "km", "minutes": "min", "ok": "OK", "not_valid": "Not valid", "field_min_length": "This field should be more than {count} characters long", "password_changed_successfully": "Password changed successfully", "plus_not_allowed_in_email": "Plus sign (+) cannot be used in email address", "terms_and_conditions": "Terms and Conditions", "error_try_again": "An error occurred, please try again", "confirm_reject_temp_address": "Are you sure you want to reject the temporary address request?", "reject_reason_suggestion": "It is recommended to add a reason for rejection to clarify the reason to the student.", "order_absence_not_found": "Order absence not found", "unknown_error": "Unknown error", "an_error_occurred": "An error occurred", "failed_to_complete_profile": "Failed to complete profile", "sign_in_canceled": "Sign in was canceled", "sign_in_failed": "Sign in failed. Please try again", "loading_requests": "Loading requests...", "loading": "Loading...", "error": "Error", "error_loading_route": "Error loading previous route", "failed_connect_websocket": "Failed to connect to WebSocket", "not_specified": "Not specified", "confirm_acceptance": "Confirm Acceptance", "Search...": "Search...", "All": "All", "Supervisor": "Supervisor", "Driver": "Driver", "Trip Type": "Trip Type", "track": "Track", "Info": "Info", "Status": "Status", "Active": "Active", "No students in bus": "No students in bus", "No trips available": "No trips available", "Waiting": "Waiting", "Present": "Present", "Absent": "Absent", "No students waiting": "No students waiting", "Unknown": "Unknown", "Absent Student": "Absent Student", "In Bus": "In Bus", "Arrived Home": "Arrived Home", "No search results": "No search results", "Supervisor & Driver": "Supervisor & Driver", "Not Found": "Not Found", "maintenance_title": "Under Maintenance", "maintenance_message": "The app is currently being updated. Please try again later.", "maintenance_checking": "Checking...", "maintenance_retry": "Try Again", "schools": "Schools", "app_updating": "App is updating"}