import 'package:flutter/material.dart';

import '../../config/app_update_config.dart';
import '../../helper/network_serviecs.dart';
import '../models/app_update_status_model.dart';

class AppUpdateStatusRepo {
  final _dio = NetworkService();

  /// Check if app is currently under maintenance or updating
  /// [appName] - The unique identifier for the app
  /// [appType] - The type of app (parents, schools, attendants)
  Future<AppUpdateStatusModel> checkAppUpdateStatus({
    required String appName,
    required String appType,
  }) async {
    try {
      // Validate app name
      if (!AppUpdateConfig.isValidAppName(appName)) {
        debugPrint('Warning: App name "$appName" is not in the supported list');
      }

      // Since the baseUrl already contains the app type (schools),
      // we only need to add the endpoint
      final String url = 'app/updating';

      final request = await _dio.get(
        url: url,
        queryParameters: {
          'name': appName,
        },
        isAuth: false, // No authentication required as per documentation
      );

      AppUpdateStatusModel? appUpdateStatusModel;

      if (request.statusCode == 200) {
        appUpdateStatusModel = AppUpdateStatusModel.fromJson(request.data);
      } else {
        // Handle error responses
        appUpdateStatusModel = AppUpdateStatusModel.fromJson(request.data);
      }

      return appUpdateStatusModel;
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("catch error $e");
      return AppUpdateStatusModel(
        success: false,
        message: e.toString(),
      );
    }
  }

  /// Check if current app is updating (simplified for schools app)
  Future<AppUpdateStatusModel> checkCurrentAppStatus({
    required String appName,
  }) async {
    try {
      // Validate app name
      if (!AppUpdateConfig.isValidAppName(appName)) {
        debugPrint('Warning: App name "$appName" is not in the supported list');
      }

      // Since the baseUrl already contains the app type (schools),
      // we only need to add the endpoint
      final String url = 'app/updating';

      final request = await _dio.get(
        url: url,
        queryParameters: {
          'name': appName,
        },
        isAuth: false, // No authentication required as per documentation
      );

      AppUpdateStatusModel? appUpdateStatusModel;

      if (request.statusCode == 200) {
        appUpdateStatusModel = AppUpdateStatusModel.fromJson(request.data);
      } else {
        // Handle error responses
        appUpdateStatusModel = AppUpdateStatusModel.fromJson(request.data);
      }

      return appUpdateStatusModel;
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("catch error $e");
      return AppUpdateStatusModel(
        success: false,
        message: e.toString(),
      );
    }
  }
}
